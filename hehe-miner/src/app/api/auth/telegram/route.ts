import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generateToken } from '@/lib/auth'
import { validateTelegramAuth, isTelegramAuthExpired, TelegramUser } from '@/lib/telegram'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('Auth request body:', body)

    // ONLY allow mock authentication in development with explicit mock flag
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_MOCK_AUTH === 'true' && body.mock === true) {
      console.log('🔧 Using mock authentication for development')
      let user = await prisma.user.findUnique({
        where: { telegramId: 'mock-telegram-id' }
      })

      if (!user) {
        user = await prisma.user.create({
          data: {
            telegramId: 'mock-telegram-id',
            username: 'mock_user',
            firstName: 'Mock',
            lastName: 'User',
            hasBasicPlan: true
          }
        })
      }

      const token = generateToken({
        id: user.id,
        telegramId: user.telegramId,
        username: user.username || undefined
      })

      return NextResponse.json({
        success: true,
        token,
        user: {
          id: user.id,
          telegramId: user.telegramId,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          totalBalance: user.totalBalance,
          miningPower: user.miningPower,
          hasBasicPlan: user.hasBasicPlan
        }
      })
    }

    // Handle Telegram Web App authentication
    let telegramData: TelegramUser

    // Check if this is initData from Telegram Web App
    if (body.initData) {
      console.log('Processing Telegram Web App initData')
      // Parse initData from Telegram Web App
      const urlParams = new URLSearchParams(body.initData)
      const userParam = urlParams.get('user')
      const authDate = urlParams.get('auth_date')
      const hash = urlParams.get('hash')

      if (!userParam || !authDate || !hash) {
        return NextResponse.json(
          { success: false, error: 'Invalid Telegram Web App data' },
          { status: 401 }
        )
      }

      const userData = JSON.parse(userParam)
      telegramData = {
        id: userData.id,
        first_name: userData.first_name,
        last_name: userData.last_name,
        username: userData.username,
        photo_url: userData.photo_url,
        auth_date: parseInt(authDate),
        hash: hash
      }
    } else {
      // Direct user data
      telegramData = body as TelegramUser
    }

    console.log('Telegram data:', telegramData)

    // Check if we have basic required data
    if (!telegramData || !telegramData.id) {
      console.log('❌ Missing required Telegram data:', {
        hasData: !!telegramData,
        hasId: telegramData?.id,
        data: telegramData
      })

      return NextResponse.json(
        {
          success: false,
          error: 'Missing user data. Please ensure you opened this app from Telegram Web App.',
          debug: {
            hasData: !!telegramData,
            hasId: telegramData?.id,
            receivedKeys: telegramData ? Object.keys(telegramData) : []
          }
        },
        { status: 401 }
      )
    }

    // Validate Telegram authentication
    const isValidAuth = validateTelegramAuth(telegramData)
    if (!isValidAuth) {
      console.log('❌ Telegram auth validation failed for user:', telegramData.id)
      return NextResponse.json(
        { success: false, error: 'Invalid Telegram authentication. Please try again or contact support.' },
        { status: 401 }
      )
    }

    // Check if auth is expired (but be lenient)
    if (telegramData.auth_date && isTelegramAuthExpired(telegramData.auth_date)) {
      console.log('⚠️ Telegram auth expired for user:', telegramData.id, 'but allowing anyway')
      // Don't reject expired auth in production - just log it
      if (process.env.NODE_ENV !== 'production') {
        return NextResponse.json(
          { success: false, error: 'Authentication expired. Please refresh and try again.' },
          { status: 401 }
        )
      }
    }

    // Find or create user
    const telegramIdString = telegramData.id.toString()
    console.log('Looking for user with telegramId:', telegramIdString)

    let user = await prisma.user.findUnique({
      where: { telegramId: telegramIdString }
    })

    console.log('Existing user found:', !!user)

    if (!user) {
      console.log('Creating new user with data:', {
        telegramId: telegramIdString,
        username: telegramData.username,
        firstName: telegramData.first_name,
        lastName: telegramData.last_name
      })

      try {
        user = await prisma.user.create({
          data: {
            telegramId: telegramIdString,
            username: telegramData.username,
            firstName: telegramData.first_name,
            lastName: telegramData.last_name
          }
        })
        console.log('User created successfully:', {
          id: user.id,
          telegramId: user.telegramId,
          username: user.username,
          firstName: user.firstName
        })
      } catch (createError) {
        console.error('Error creating user:', createError)
        throw createError
      }
    } else {
      console.log('Using existing user:', {
        id: user.id,
        telegramId: user.telegramId,
        username: user.username,
        firstName: user.firstName
      })
    }

    const token = generateToken({
      id: user.id,
      telegramId: user.telegramId,
      username: user.username || undefined
    })

    return NextResponse.json({
      success: true,
      token,
      user: {
        id: user.id,
        telegramId: user.telegramId,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        totalBalance: user.totalBalance,
        miningPower: user.miningPower,
        hasBasicPlan: user.hasBasicPlan
      }
    })
  } catch (error) {
    console.error('Auth error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
